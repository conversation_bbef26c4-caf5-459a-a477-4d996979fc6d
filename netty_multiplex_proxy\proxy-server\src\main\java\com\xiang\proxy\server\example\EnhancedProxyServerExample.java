package com.xiang.proxy.server.example;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.core.ActiveConnectionManager;
import com.xiang.proxy.server.core.EnhancedProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.core.ProxyResponse;
import com.xiang.proxy.server.pool.EnhancedConnectionPool;
import com.xiang.proxy.server.router.Router;
import com.xiang.proxy.server.router.impl.SimpleRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 增强代理服务器使用示例
 * 展示如何使用改进的队列机制和连接复用功能
 */
public class EnhancedProxyServerExample {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedProxyServerExample.class);
    
    public static void main(String[] args) {
        // 创建示例实例
        EnhancedProxyServerExample example = new EnhancedProxyServerExample();
        
        try {
            // 运行示例
            example.runExample();
        } catch (Exception e) {
            logger.error("运行示例时发生异常", e);
        }
    }
    
    public void runExample() throws InterruptedException {
        logger.info("启动增强代理服务器示例...");
        
        // 1. 创建路由器
        Router router = createRouter();
        
        // 2. 创建配置
        ProxyProcessorConfig config = createConfig();
        
        // 3. 创建增强的代理处理器
        EnhancedProxyProcessor processor = new EnhancedProxyProcessor(router, config);
        
        // 4. 创建增强连接池
        EnhancedConnectionPool connectionPool = new EnhancedConnectionPool();
        
        try {
            // 5. 启动处理器
            processor.start();
            logger.info("增强代理处理器已启动");
            
            // 6. 启动监控任务
            startMonitoring(processor, connectionPool);
            
            // 7. 模拟请求处理
            simulateRequests(processor);
            
            // 8. 等待一段时间观察效果
            Thread.sleep(60000); // 等待1分钟
            
        } finally {
            // 9. 关闭资源
            processor.stop();
            connectionPool.shutdown();
            logger.info("增强代理服务器示例结束");
        }
    }
    
    /**
     * 创建路由器
     */
    private Router createRouter() {
        SimpleRouter router = new SimpleRouter();
        
        // 添加路由规则示例
        router.addRoute("example.com", 80, "tcp-outbound");
        router.addRoute("api.example.com", 443, "https-outbound");
        router.addRoute("*", 80, "default-outbound");
        
        logger.info("路由器创建完成");
        return router;
    }
    
    /**
     * 创建配置
     */
    private ProxyProcessorConfig createConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 队列配置
        config.setQueueCount(4);                    // 4个队列
        config.setQueueCapacity(1000);              // 每个队列容量1000
        config.setWorkerThreadPrefix("enhanced-worker-");
        
        // 批处理配置
        config.setBatchSize(10);                    // 批处理大小
        config.setBatchTimeoutMs(100);              // 批处理超时100ms
        
        // 自适应配置
        config.setEnableAdaptiveAdjustment(true);   // 启用自适应调整
        
        // 超时配置
        config.setShutdownTimeoutSeconds(10);       // 关闭超时10秒
        
        logger.info("配置创建完成: {}", config);
        return config;
    }
    
    /**
     * 启动监控任务
     */
    private void startMonitoring(EnhancedProxyProcessor processor, EnhancedConnectionPool connectionPool) {
        ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "monitor-thread");
            t.setDaemon(true);
            return t;
        });
        
        // 每10秒输出一次统计信息
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                printStatistics(processor, connectionPool);
            } catch (Exception e) {
                logger.error("监控任务执行异常", e);
            }
        }, 10, 10, TimeUnit.SECONDS);
        
        logger.info("监控任务已启动");
    }
    
    /**
     * 打印统计信息
     */
    private void printStatistics(EnhancedProxyProcessor processor, EnhancedConnectionPool connectionPool) {
        logger.info("=== 统计信息 ===");
        
        // 队列统计
        try {
            var queueStats = processor.getQueueStats();
            logger.info("队列统计: 总队列大小={}, 已处理请求={}", 
                queueStats.getTotalQueueSize(), queueStats.getProcessedRequests());
        } catch (Exception e) {
            logger.warn("获取队列统计失败", e);
        }
        
        // 连接管理器统计
        try {
            Map<String, ActiveConnectionManager.ConnectionStats> connectionStats = processor.getConnectionStats();
            for (Map.Entry<String, ActiveConnectionManager.ConnectionStats> entry : connectionStats.entrySet()) {
                logger.info("连接管理器 {}: {}", entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            logger.warn("获取连接统计失败", e);
        }
        
        // 连接池统计
        try {
            EnhancedConnectionPool.PoolStats poolStats = connectionPool.getStats();
            logger.info("连接池统计: {}", poolStats);
        } catch (Exception e) {
            logger.warn("获取连接池统计失败", e);
        }
        
        // 自适应管理器建议
        try {
            if (processor.getAdaptiveManager() != null) {
                String recommendations = processor.getAdaptiveManager().getAdjustmentRecommendations();
                logger.info("自适应建议:\n{}", recommendations);
            }
        } catch (Exception e) {
            logger.warn("获取自适应建议失败", e);
        }
        
        logger.info("=== 统计信息结束 ===");
    }
    
    /**
     * 模拟请求处理
     */
    private void simulateRequests(EnhancedProxyProcessor processor) {
        ScheduledExecutorService requestExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "request-simulator");
            t.setDaemon(true);
            return t;
        });
        
        // 模拟不同类型的请求
        requestExecutor.scheduleAtFixedRate(() -> {
            simulateHttpRequest(processor);
        }, 1, 2, TimeUnit.SECONDS);
        
        requestExecutor.scheduleAtFixedRate(() -> {
            simulateHttpsRequest(processor);
        }, 2, 3, TimeUnit.SECONDS);
        
        requestExecutor.scheduleAtFixedRate(() -> {
            simulateBatchRequests(processor);
        }, 5, 10, TimeUnit.SECONDS);
        
        logger.info("请求模拟器已启动");
    }
    
    /**
     * 模拟HTTP请求
     */
    private void simulateHttpRequest(EnhancedProxyProcessor processor) {
        try {
            ProxyRequest request = ProxyRequest.builder()
                .requestId("http-" + System.currentTimeMillis())
                .clientId("client-1")
                .targetHost("example.com")
                .targetPort(80)
                .protocol("HTTP")
                .build();
                
            CompletableFuture<ProxyResponse> future = processor.processRequest(request);
            future.whenComplete((response, throwable) -> {
                if (throwable != null) {
                    logger.warn("HTTP请求处理失败: {}", request.getRequestId(), throwable);
                } else {
                    logger.debug("HTTP请求处理成功: {} -> {}", request.getRequestId(), response.getStatus());
                }
            });
        } catch (Exception e) {
            logger.error("模拟HTTP请求时发生异常", e);
        }
    }
    
    /**
     * 模拟HTTPS请求
     */
    private void simulateHttpsRequest(EnhancedProxyProcessor processor) {
        try {
            ProxyRequest request = ProxyRequest.builder()
                .requestId("https-" + System.currentTimeMillis())
                .clientId("client-2")
                .targetHost("api.example.com")
                .targetPort(443)
                .protocol("HTTPS")
                .build();
                
            CompletableFuture<ProxyResponse> future = processor.processRequest(request);
            future.whenComplete((response, throwable) -> {
                if (throwable != null) {
                    logger.warn("HTTPS请求处理失败: {}", request.getRequestId(), throwable);
                } else {
                    logger.debug("HTTPS请求处理成功: {} -> {}", request.getRequestId(), response.getStatus());
                }
            });
        } catch (Exception e) {
            logger.error("模拟HTTPS请求时发生异常", e);
        }
    }
    
    /**
     * 模拟批量请求
     */
    private void simulateBatchRequests(EnhancedProxyProcessor processor) {
        try {
            for (int i = 0; i < 5; i++) {
                ProxyRequest request = ProxyRequest.builder()
                    .requestId("batch-" + System.currentTimeMillis() + "-" + i)
                    .clientId("batch-client")
                    .targetHost("batch.example.com")
                    .targetPort(80)
                    .protocol("HTTP")
                    .build();
                    
                CompletableFuture<ProxyResponse> future = processor.processRequest(request);
                future.whenComplete((response, throwable) -> {
                    if (throwable != null) {
                        logger.warn("批量请求处理失败: {}", request.getRequestId(), throwable);
                    } else {
                        logger.debug("批量请求处理成功: {} -> {}", request.getRequestId(), response.getStatus());
                    }
                });
            }
            logger.debug("提交了5个批量请求");
        } catch (Exception e) {
            logger.error("模拟批量请求时发生异常", e);
        }
    }
}
