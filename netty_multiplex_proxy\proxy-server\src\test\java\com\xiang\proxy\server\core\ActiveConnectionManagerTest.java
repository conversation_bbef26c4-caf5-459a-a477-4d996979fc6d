package com.xiang.proxy.server.core;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ActiveConnectionManager 测试类
 */
public class ActiveConnectionManagerTest {
    
    private ActiveConnectionManager connectionManager;
    
    @Mock
    private Channel mockChannel;
    
    @Mock
    private ChannelFuture mockChannelFuture;
    
    private AutoCloseable closeable;
    
    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        connectionManager = new ActiveConnectionManager();
        
        // 设置mock行为
        when(mockChannel.isActive()).thenReturn(true);
        when(mockChannel.isWritable()).thenReturn(true);
        when(mockChannelFuture.isDone()).thenReturn(true);
        when(mockChannelFuture.isSuccess()).thenReturn(true);
        when(mockChannelFuture.channel()).thenReturn(mockChannel);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (closeable != null) {
            closeable.close();
        }
    }
    
    @Test
    void testGetOrCreateConnection() {
        // 测试创建新连接
        ActiveConnectionManager.ActiveConnection connection1 = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        assertNotNull(connection1);
        assertEquals("example.com", connection1.getHost());
        assertEquals(80, connection1.getPort());
        assertEquals("client1", connection1.getClientId());
        assertEquals("HTTP", connection1.getProtocol());
        
        // 测试获取已存在的连接
        ActiveConnectionManager.ActiveConnection connection2 = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        assertSame(connection1, connection2);
        
        // 测试不同参数创建不同连接
        ActiveConnectionManager.ActiveConnection connection3 = 
            connectionManager.getOrCreateConnection("example.com", 80, "client2", "HTTP");
        
        assertNotSame(connection1, connection3);
    }
    
    @Test
    void testRemoveConnection() {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        assertNotNull(connection);
        
        // 移除连接
        connectionManager.removeConnection("example.com", 80, "client1", "HTTP");
        
        // 再次获取应该创建新连接
        ActiveConnectionManager.ActiveConnection newConnection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        assertNotSame(connection, newConnection);
    }
    
    @Test
    void testSendMessage() {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        // 设置ChannelFuture
        connection.setChannelFuture(mockChannelFuture);
        
        // 创建测试消息
        ByteBuf message = Unpooled.copiedBuffer("test message".getBytes());
        
        // 发送消息
        boolean result = connection.sendMessage(message);
        
        assertTrue(result);
        verify(mockChannel).writeAndFlush(any(ByteBuf.class));
    }
    
    @Test
    void testSendMessageWithInactiveChannel() {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        // 设置Channel为非活跃状态
        when(mockChannel.isActive()).thenReturn(false);
        connection.setChannelFuture(mockChannelFuture);
        
        // 创建测试消息
        ByteBuf message = Unpooled.copiedBuffer("test message".getBytes());
        
        // 发送消息（应该加入队列）
        boolean result = connection.sendMessage(message);
        
        assertTrue(result);
        assertEquals(1, connection.getQueueSize());
        
        // 验证没有直接发送
        verify(mockChannel, never()).writeAndFlush(any(ByteBuf.class));
    }
    
    @Test
    void testFlushQueuedMessages() {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        // 先设置Channel为非活跃状态，添加消息到队列
        when(mockChannel.isActive()).thenReturn(false);
        connection.setChannelFuture(mockChannelFuture);
        
        ByteBuf message1 = Unpooled.copiedBuffer("message1".getBytes());
        ByteBuf message2 = Unpooled.copiedBuffer("message2".getBytes());
        
        connection.sendMessage(message1);
        connection.sendMessage(message2);
        
        assertEquals(2, connection.getQueueSize());
        
        // 然后设置Channel为活跃状态，刷新队列
        when(mockChannel.isActive()).thenReturn(true);
        connection.flushQueuedMessages(mockChannel);
        
        assertEquals(0, connection.getQueueSize());
        verify(mockChannel, times(2)).write(any(ByteBuf.class));
        verify(mockChannel).flush();
    }
    
    @Test
    void testConnectionTimeout() throws InterruptedException {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        // 等待一小段时间
        Thread.sleep(100);
        
        // 检查超时（使用很短的超时时间）
        long currentTime = System.currentTimeMillis();
        boolean isTimeout = connection.isTimeout(currentTime, 50); // 50ms超时
        
        assertTrue(isTimeout);
    }
    
    @Test
    void testCleanupTimeoutConnections() throws InterruptedException {
        // 创建多个连接
        connectionManager.getOrCreateConnection("example1.com", 80, "client1", "HTTP");
        connectionManager.getOrCreateConnection("example2.com", 80, "client2", "HTTP");
        connectionManager.getOrCreateConnection("example3.com", 80, "client3", "HTTP");
        
        ActiveConnectionManager.ConnectionStats statsBefore = connectionManager.getStats();
        assertEquals(3, statsBefore.getActiveConnections());
        
        // 等待一段时间
        Thread.sleep(100);
        
        // 清理超时连接（使用很短的超时时间进行测试）
        int cleaned = connectionManager.cleanupTimeoutConnections();
        
        // 由于我们使用的是默认超时时间（60秒），所以不会清理任何连接
        assertEquals(0, cleaned);
        
        ActiveConnectionManager.ConnectionStats statsAfter = connectionManager.getStats();
        assertEquals(3, statsAfter.getActiveConnections());
    }
    
    @Test
    void testConnectionStats() {
        // 初始统计
        ActiveConnectionManager.ConnectionStats initialStats = connectionManager.getStats();
        assertEquals(0, initialStats.getTotalConnections());
        assertEquals(0, initialStats.getActiveConnections());
        
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection1 = 
            connectionManager.getOrCreateConnection("example1.com", 80, "client1", "HTTP");
        ActiveConnectionManager.ActiveConnection connection2 = 
            connectionManager.getOrCreateConnection("example2.com", 80, "client2", "HTTP");
        
        // 检查统计
        ActiveConnectionManager.ConnectionStats stats = connectionManager.getStats();
        assertEquals(2, stats.getTotalConnections());
        assertEquals(2, stats.getActiveConnections());
        
        // 发送消息
        connection1.setChannelFuture(mockChannelFuture);
        ByteBuf message = Unpooled.copiedBuffer("test".getBytes());
        connection1.sendMessage(message);
        
        // 检查消息统计
        ActiveConnectionManager.ConnectionStats statsAfterMessage = connectionManager.getStats();
        assertEquals(1, statsAfterMessage.getTotalMessages());
    }
    
    @Test
    void testConnectionKey() {
        // 测试ConnectionKey的equals和hashCode
        ActiveConnectionManager.ConnectionKey key1 = 
            new ActiveConnectionManager.ConnectionKey("example.com", 80, "client1", "HTTP");
        ActiveConnectionManager.ConnectionKey key2 = 
            new ActiveConnectionManager.ConnectionKey("example.com", 80, "client1", "HTTP");
        ActiveConnectionManager.ConnectionKey key3 = 
            new ActiveConnectionManager.ConnectionKey("example.com", 80, "client2", "HTTP");
        
        assertEquals(key1, key2);
        assertEquals(key1.hashCode(), key2.hashCode());
        assertNotEquals(key1, key3);
        assertNotEquals(key1.hashCode(), key3.hashCode());
        
        // 测试toString
        String keyString = key1.toString();
        assertTrue(keyString.contains("example.com"));
        assertTrue(keyString.contains("80"));
        assertTrue(keyString.contains("client1"));
        assertTrue(keyString.contains("HTTP"));
    }
    
    @Test
    void testMessageQueueLimit() {
        // 创建连接
        ActiveConnectionManager.ActiveConnection connection = 
            connectionManager.getOrCreateConnection("example.com", 80, "client1", "HTTP");
        
        // 设置Channel为非活跃状态
        when(mockChannel.isActive()).thenReturn(false);
        connection.setChannelFuture(mockChannelFuture);
        
        // 尝试发送大量消息，测试队列限制
        int successCount = 0;
        for (int i = 0; i < 1500; i++) { // 超过MAX_QUEUE_SIZE(1000)
            ByteBuf message = Unpooled.copiedBuffer(("message" + i).getBytes());
            if (connection.sendMessage(message)) {
                successCount++;
            }
        }
        
        // 应该只有1000个消息成功加入队列
        assertEquals(1000, successCount);
        assertEquals(1000, connection.getQueueSize());
    }
}
